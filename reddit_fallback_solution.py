#!/usr/bin/env python3
"""
Reddit API 403错误临时解决方案
提供多种应对策略和替代数据源
"""

import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
import argparse

def check_existing_data():
    """检查现有的社交媒体数据"""
    print("=== 检查现有社交媒体数据 ===")
    
    social_media_dir = Path("social_media_data")
    if not social_media_dir.exists():
        print("❌ 没有找到social_media_data目录")
        return {}
    
    data_summary = {}
    
    # 检查各股票的数据
    tickers = ['AAPL', 'MSFT', 'NVDA']
    
    for ticker in tickers:
        ticker_dir = social_media_dir / f"{ticker}_social_media"
        if ticker_dir.exists():
            files = list(ticker_dir.glob("reddit_*.json"))
            if files:
                data_summary[ticker] = {
                    'files': len(files),
                    'date_range': [],
                    'total_posts': 0
                }
                
                dates = []
                total_posts = 0
                
                for file in files:
                    try:
                        with open(file, 'r', encoding='utf-8') as f:
                            posts = json.load(f)
                            total_posts += len(posts)
                            
                        # 从文件名提取日期
                        date_str = file.stem.replace('reddit_', '')
                        dates.append(date_str)
                    except Exception as e:
                        print(f"  ⚠ 读取文件失败 {file}: {e}")
                
                if dates:
                    dates.sort()
                    data_summary[ticker]['date_range'] = [dates[0], dates[-1]]
                    data_summary[ticker]['total_posts'] = total_posts
                    
                print(f"✓ {ticker}: {len(files)} 个文件, {total_posts} 个帖子")
                print(f"  日期范围: {dates[0]} 到 {dates[-1]}")
            else:
                print(f"⚠ {ticker}: 目录存在但没有数据文件")
        else:
            print(f"❌ {ticker}: 没有数据目录")
    
    return data_summary

def create_reddit_retry_script():
    """创建Reddit重试脚本"""
    print("\n=== 创建Reddit重试脚本 ===")
    
    retry_script = '''#!/usr/bin/env python3
"""
Reddit API重试脚本 - 自动处理403错误
"""

import time
import subprocess
import sys
from datetime import datetime

def retry_reddit_collection():
    """重试Reddit数据收集"""
    max_attempts = 5
    base_delay = 3600  # 1小时
    
    for attempt in range(max_attempts):
        print(f"\\n=== 尝试 {attempt + 1}/{max_attempts} ===")
        print(f"时间: {datetime.now()}")
        
        try:
            # 运行Reddit收集脚本
            result = subprocess.run([
                sys.executable, "reddit_live_collector.py",
                "--start-date", "2024-12-01",
                "--end-date", "2025-02-01", 
                "--tickers", "AAPL", "MSFT", "NVDA",
                "--limit-per-subreddit", "20"
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ Reddit数据收集成功!")
                print(result.stdout)
                return True
            else:
                print("❌ Reddit数据收集失败")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ 脚本执行超时")
        except Exception as e:
            print(f"❌ 执行错误: {e}")
        
        if attempt < max_attempts - 1:
            delay = base_delay * (2 ** attempt)  # 指数退避
            print(f"等待 {delay/3600:.1f} 小时后重试...")
            time.sleep(delay)
    
    print("❌ 所有重试都失败了")
    return False

if __name__ == '__main__':
    retry_reddit_collection()
'''
    
    with open('reddit_retry.py', 'w', encoding='utf-8') as f:
        f.write(retry_script)
    
    print("✅ 创建了reddit_retry.py脚本")
    print("   使用方法: python reddit_retry.py")
    print("   该脚本会自动重试，使用指数退避策略")

def suggest_alternative_approaches():
    """建议替代方案"""
    print("\n=== 替代解决方案 ===")
    
    print("1. 🕐 时间策略:")
    print("   • 等待1-2小时后重试 (最常见的解决方案)")
    print("   • 避开美国东部时间的高峰期 (9:00-17:00)")
    print("   • 尝试在深夜或早晨运行脚本")
    
    print("\n2. 🔧 技术策略:")
    print("   • 使用更长的延迟间隔 (5-10秒)")
    print("   • 减少每次请求的数据量 (limit=10-20)")
    print("   • 使用指数退避重试机制")
    
    print("\n3. 📊 数据策略:")
    print("   • 使用现有的本地数据进行回测")
    print("   • 专注于已有数据的分析和优化")
    print("   • 考虑其他数据源 (新闻API等)")
    
    print("\n4. 🌐 网络策略:")
    print("   • 尝试更换网络环境 (VPN/代理)")
    print("   • 检查防火墙设置")
    print("   • 使用不同的IP地址")

def create_monitoring_script():
    """创建监控脚本"""
    print("\n=== 创建Reddit状态监控脚本 ===")
    
    monitor_script = '''#!/usr/bin/env python3
"""
Reddit API状态监控脚本
定期检查Reddit API是否恢复正常
"""

import time
import praw
import prawcore
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

def check_reddit_status():
    """检查Reddit API状态"""
    try:
        reddit = praw.Reddit(
            client_id=os.getenv('REDDIT_CLIENT_ID'),
            client_secret=os.getenv('REDDIT_CLIENT_SECRET'),
            user_agent=os.getenv('REDDIT_USER_AGENT'),
            username=os.getenv('REDDIT_USERNAME'),
            password=os.getenv('REDDIT_PASSWORD')
        )
        
        # 测试基本访问
        subreddit = reddit.subreddit('test')
        posts = list(subreddit.new(limit=1))
        
        return True, f"成功获取 {len(posts)} 个帖子"
        
    except prawcore.exceptions.ResponseException as e:
        if e.response.status_code == 403:
            return False, "403错误 - 仍然被限制"
        else:
            return False, f"其他错误: {e}"
    except Exception as e:
        return False, f"连接错误: {e}"

def monitor_reddit():
    """监控Reddit API状态"""
    print("开始监控Reddit API状态...")
    print("按Ctrl+C停止监控")
    
    check_interval = 1800  # 30分钟检查一次
    
    try:
        while True:
            print(f"\\n[{datetime.now()}] 检查Reddit API状态...")
            
            success, message = check_reddit_status()
            
            if success:
                print(f"✅ Reddit API恢复正常: {message}")
                print("可以重新运行数据收集脚本!")
                break
            else:
                print(f"❌ Reddit API仍不可用: {message}")
                print(f"等待 {check_interval/60} 分钟后再次检查...")
                time.sleep(check_interval)
                
    except KeyboardInterrupt:
        print("\\n监控已停止")

if __name__ == '__main__':
    monitor_reddit()
'''
    
    with open('reddit_monitor.py', 'w', encoding='utf-8') as f:
        f.write(monitor_script)
    
    print("✅ 创建了reddit_monitor.py脚本")
    print("   使用方法: python reddit_monitor.py")
    print("   该脚本会每30分钟检查一次Reddit API状态")

def main():
    parser = argparse.ArgumentParser(description='Reddit API 403错误解决方案')
    parser.add_argument('--check-data', action='store_true', help='检查现有数据')
    parser.add_argument('--create-scripts', action='store_true', help='创建辅助脚本')
    parser.add_argument('--all', action='store_true', help='执行所有操作')
    
    args = parser.parse_args()
    
    if args.all or args.check_data:
        data_summary = check_existing_data()
        
        if data_summary:
            print(f"\\n✅ 找到现有数据，可以使用本地数据进行回测")
            print(f"建议运行: python src/backtester.py --use_local_social_media")
        else:
            print(f"\\n⚠ 没有找到足够的本地数据")
    
    if args.all or args.create_scripts:
        create_reddit_retry_script()
        create_monitoring_script()
    
    if not any([args.check_data, args.create_scripts, args.all]):
        suggest_alternative_approaches()
        print(f"\\n使用 --help 查看更多选项")

if __name__ == '__main__':
    main()
