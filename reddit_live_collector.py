#!/usr/bin/env python3
"""
Reddit实时数据收集器

从Reddit API获取股票相关讨论数据，支持增量更新和情感分析
与AI对冲基金回测系统完全兼容
"""

import os
import json
import time
import logging
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
import argparse
from dataclasses import dataclass
import re

import praw
import prawcore
from dotenv import load_dotenv
from tqdm import tqdm

# 加载环境变量
load_dotenv()

@dataclass
class RedditConfig:
    """Reddit API配置"""
    client_id: str
    client_secret: str
    user_agent: str
    username: Optional[str] = None
    password: Optional[str] = None

class RedditLiveCollector:
    """Reddit实时数据收集器"""
    
    def __init__(self, config: RedditConfig, output_dir: str = "social_media_data"):
        """
        初始化Reddit收集器
        
        Args:
            config: Reddit API配置
            output_dir: 输出目录
        """
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Reddit API客户端 (只读模式，更稳定)
        self.reddit = praw.Reddit(
            client_id=config.client_id,
            client_secret=config.client_secret,
            user_agent=config.user_agent
        )

        # 配置日志
        self.setup_logging()

        # 验证API认证
        self.verify_authentication()
        
        # 股票配置
        self.ticker_keywords = {
            'AAPL': ['Apple', 'AAPL', 'Apple Inc', 'iPhone', 'iPad', 'Mac', 'iOS', 'Tim Cook'],
            'MSFT': ['Microsoft', 'MSFT', 'Windows', 'Azure', 'Office', 'Xbox', 'Satya Nadella'],
            'NVDA': ['NVIDIA', 'NVDA', 'GeForce', 'RTX', 'GPU', 'AI chip', 'Jensen Huang'],
            'GOOGL': ['Google', 'GOOGL', 'Alphabet', 'YouTube', 'Android', 'Chrome', 'Sundar Pichai'],
            'AMZN': ['Amazon', 'AMZN', 'AWS', 'Prime', 'Alexa', 'Jeff Bezos', 'Andy Jassy'],
            'TSLA': ['Tesla', 'TSLA', 'Elon Musk', 'Model S', 'Model 3', 'Model Y', 'Cybertruck'],
            'META': ['Meta', 'META', 'Facebook', 'Instagram', 'WhatsApp', 'Mark Zuckerberg'],
            'NFLX': ['Netflix', 'NFLX', 'streaming', 'Reed Hastings'],
            'AMD': ['AMD', 'Ryzen', 'Radeon', 'Lisa Su'],
            'INTC': ['Intel', 'INTC', 'Core', 'Xeon', 'Pat Gelsinger']
        }
        
        # 目标子版块
        self.target_subreddits = [
            'stocks', 'investing', 'wallstreetbets', 'SecurityAnalysis',
            'ValueInvesting', 'financialindependence', 'StockMarket',
            'options', 'pennystocks', 'dividends', 'investing_discussion',
            'finance', 'business', 'technology', 'apple', 'microsoft',
            'nvidia', 'tesla', 'amazon', 'google'
        ]
        
        # 情感分析关键词
        self.sentiment_keywords = {
            'bullish': [
                'buy', 'bull', 'bullish', 'long', 'calls', 'moon', 'rocket',
                'pump', 'surge', 'rally', 'breakout', 'uptrend', 'strong',
                'positive', 'optimistic', 'growth', 'profit', 'earnings beat'
            ],
            'bearish': [
                'sell', 'bear', 'bearish', 'short', 'puts', 'crash', 'dump',
                'drop', 'fall', 'decline', 'downtrend', 'weak', 'negative',
                'pessimistic', 'loss', 'earnings miss', 'overvalued'
            ]
        }
        
        # 已处理帖子ID缓存
        self.processed_posts: Set[str] = set()
        self.load_processed_posts()
    
    def setup_logging(self):
        """设置日志配置"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "reddit_collector.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def verify_authentication(self):
        """验证Reddit API认证"""
        try:
            # 测试基本认证 - 尝试访问一个简单的公开子版块
            test_subreddit = self.reddit.subreddit('test')
            _ = test_subreddit.display_name
            self.logger.info("Reddit API认证验证成功 (只读模式)")
        except prawcore.exceptions.ResponseException as e:
            if e.response.status_code == 401:
                self.logger.error("Reddit API认证失败 - 401 Unauthorized")
                self.logger.error("请检查以下配置:")
                self.logger.error("1. REDDIT_CLIENT_ID 和 REDDIT_CLIENT_SECRET 是否正确")
                self.logger.error("2. 在 https://www.reddit.com/prefs/apps 创建的应用类型是否为 'script'")
                self.logger.error("3. 确认User-Agent格式正确")
                raise ValueError("Reddit API认证失败")
            elif e.response.status_code == 403:
                self.logger.error("Reddit API访问被禁止 - 403 Forbidden")
                self.logger.error("可能原因:")
                self.logger.error("1. Reddit应用类型设置错误 (应该是'script')")
                self.logger.error("2. User-Agent格式不正确")
                self.logger.error("3. IP地址被Reddit限制")
                raise ValueError("Reddit API访问被禁止")
            else:
                self.logger.error(f"Reddit API验证失败: {e}")
                raise
        except Exception as e:
            self.logger.error(f"Reddit API验证失败: {e}")
            raise

    def load_processed_posts(self):
        """加载已处理的帖子ID"""
        cache_file = self.output_dir / "processed_posts_cache.json"
        if cache_file.exists():
            try:
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                    self.processed_posts = set(data.get('processed_posts', []))
                self.logger.info(f"加载了 {len(self.processed_posts)} 个已处理帖子ID")
            except Exception as e:
                self.logger.warning(f"加载缓存失败: {e}")
    
    def save_processed_posts(self):
        """保存已处理的帖子ID"""
        cache_file = self.output_dir / "processed_posts_cache.json"
        try:
            with open(cache_file, 'w') as f:
                json.dump({
                    'processed_posts': list(self.processed_posts),
                    'last_updated': datetime.now().isoformat()
                }, f, indent=2)
        except Exception as e:
            self.logger.error(f"保存缓存失败: {e}")
    
    def extract_tickers_from_text(self, text: str) -> List[str]:
        """从文本中提取股票代码"""
        if not text:
            return []
        
        text_lower = text.lower()
        found_tickers = []
        
        for ticker, keywords in self.ticker_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    found_tickers.append(ticker)
                    break
        
        # 直接匹配股票代码格式 $TICKER
        ticker_pattern = r'\$([A-Z]{1,5})\b'
        matches = re.findall(ticker_pattern, text.upper())
        for match in matches:
            if match in self.ticker_keywords:
                found_tickers.append(match)
        
        return list(set(found_tickers))
    
    def determine_sentiment(self, text: str) -> str:
        """确定文本情感倾向"""
        if not text:
            return 'neutral'
        
        text_lower = text.lower()
        bullish_score = 0
        bearish_score = 0
        
        for keyword in self.sentiment_keywords['bullish']:
            bullish_score += text_lower.count(keyword)
        
        for keyword in self.sentiment_keywords['bearish']:
            bearish_score += text_lower.count(keyword)
        
        if bullish_score > bearish_score:
            return 'bullish'
        elif bearish_score > bullish_score:
            return 'bearish'
        else:
            return 'neutral'
    
    def calculate_engagement_score(self, upvotes: int, comments_count: int) -> float:
        """计算参与度分数"""
        return upvotes * 1.0 + comments_count * 2.0
    
    def process_submission(self, submission) -> Optional[Dict[str, Any]]:
        """处理Reddit提交"""
        try:
            # 检查是否已处理
            if submission.id in self.processed_posts:
                return None
            
            # 获取文本内容
            title = submission.title or ""
            content = submission.selftext or ""
            full_text = f"{title} {content}"
            
            # 提取相关股票代码
            tickers = self.extract_tickers_from_text(full_text)
            if not tickers:
                return None
            
            # 创建帖子数据
            created_time = datetime.fromtimestamp(
                submission.created_utc, tz=timezone.utc
            ).replace(tzinfo=None)
            
            post_data = {
                'platform': 'reddit',
                'post_id': f"reddit_{submission.id}",
                'title': title,
                'content': content,
                'author': str(submission.author) if submission.author else 'deleted',
                'created_time': created_time.isoformat(),
                'url': f"https://reddit.com{submission.permalink}",
                'upvotes': submission.score,
                'comments_count': submission.num_comments,
                'tickers': tickers,
                'sentiment': self.determine_sentiment(full_text),
                'engagement_score': self.calculate_engagement_score(
                    submission.score, submission.num_comments
                ),
                'source_subreddit': submission.subreddit.display_name,
                'hashtags': None
            }
            
            # 标记为已处理
            self.processed_posts.add(submission.id)
            
            return post_data
            
        except Exception as e:
            self.logger.error(f"处理提交失败 {submission.id}: {e}")
            return None
    
    def save_posts_by_date_and_ticker(self, posts: List[Dict[str, Any]]):
        """按日期和股票代码保存帖子"""
        posts_by_ticker_date = {}
        
        for post in posts:
            created_time = datetime.fromisoformat(post['created_time'])
            date_str = created_time.strftime('%Y-%m-%d')
            
            for ticker in post['tickers']:
                key = (ticker, date_str)
                if key not in posts_by_ticker_date:
                    posts_by_ticker_date[key] = []
                
                # 为每个股票创建单独的帖子记录
                ticker_post = post.copy()
                ticker_post['ticker'] = ticker
                del ticker_post['tickers']  # 移除tickers列表
                
                posts_by_ticker_date[key].append(ticker_post)
        
        # 保存到文件
        for (ticker, date_str), ticker_posts in posts_by_ticker_date.items():
            ticker_dir = self.output_dir / f"{ticker}_social_media"
            ticker_dir.mkdir(parents=True, exist_ok=True)
            
            file_path = ticker_dir / f"reddit_{date_str}.json"
            
            # 如果文件已存在，合并数据
            existing_posts = []
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        existing_posts = json.load(f)
                except Exception as e:
                    self.logger.warning(f"读取现有文件失败 {file_path}: {e}")
            
            # 合并并去重
            existing_ids = {post.get('post_id') for post in existing_posts}
            new_posts = [post for post in ticker_posts 
                        if post.get('post_id') not in existing_ids]
            
            if new_posts:
                all_posts = existing_posts + new_posts
                
                # 按时间排序
                all_posts.sort(key=lambda x: x.get('created_time', ''))
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(all_posts, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"保存 {ticker} {date_str}: {len(new_posts)} 个新帖子")
    
    def collect_from_subreddit(self, subreddit_name: str,
                              start_date: datetime, end_date: datetime,
                              limit: int = 1000) -> List[Dict[str, Any]]:
        """从指定子版块收集数据 - 带重试机制处理403错误"""
        posts = []
        max_retries = 3
        base_delay = 60  # 基础延迟60秒

        for attempt in range(max_retries):
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                self.logger.info(f"开始收集 r/{subreddit_name} 数据... (尝试 {attempt + 1}/{max_retries})")

                # 测试子版块访问权限
                try:
                    # 先测试能否访问子版块基本信息
                    _ = subreddit.display_name
                    self.logger.debug(f"成功访问 r/{subreddit_name}")
                except prawcore.exceptions.Forbidden:
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        self.logger.warning(f"r/{subreddit_name} 访问被禁止，等待 {delay} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"r/{subreddit_name} 访问被禁止 (多次重试失败)")
                        return posts
                except prawcore.exceptions.NotFound:
                    self.logger.error(f"r/{subreddit_name} 不存在")
                    return posts
                except prawcore.exceptions.ResponseException as e:
                    if e.response.status_code == 403:
                        if attempt < max_retries - 1:
                            delay = base_delay * (2 ** attempt)
                            self.logger.warning(f"r/{subreddit_name} 403错误，等待 {delay} 秒后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            self.logger.error(f"r/{subreddit_name} 403错误 (多次重试失败) - 可能是临时限制")
                            return posts
                    else:
                        raise

                # 获取最新帖子 - 减少批量大小以避免限制
                actual_limit = min(limit, 50)  # 限制每次最多50个帖子
                try:
                    submissions = subreddit.new(limit=actual_limit)
                except prawcore.exceptions.ResponseException as e:
                    if e.response.status_code == 403:
                        if attempt < max_retries - 1:
                            delay = base_delay * (2 ** attempt)
                            self.logger.warning(f"获取帖子403错误，等待 {delay} 秒后重试...")
                            time.sleep(delay)
                            continue
                        else:
                            self.logger.error(f"获取帖子403错误 (多次重试失败)")
                            return posts
                    else:
                        raise

                # 处理帖子
                submission_count = 0
                for submission in tqdm(submissions, desc=f"r/{subreddit_name}"):
                    try:
                        submission_count += 1
                        created_time = datetime.fromtimestamp(
                            submission.created_utc, tz=timezone.utc
                        ).replace(tzinfo=None)

                        # 检查时间范围
                        if not (start_date <= created_time <= end_date):
                            continue

                        post_data = self.process_submission(submission)
                        if post_data:
                            posts.append(post_data)

                        # API限制控制 - 增加延迟
                        time.sleep(0.5)

                    except prawcore.exceptions.TooManyRequests:
                        self.logger.warning("API限制，等待60秒...")
                        time.sleep(60)
                        continue
                    except prawcore.exceptions.ResponseException as e:
                        if e.response.status_code == 403:
                            self.logger.warning(f"处理提交时403错误: {e}")
                            break  # 停止处理这个子版块
                        else:
                            self.logger.error(f"处理提交失败: {e}")
                            continue
                    except Exception as e:
                        self.logger.error(f"处理提交失败: {e}")
                        continue

                self.logger.info(f"r/{subreddit_name} 成功处理了 {submission_count} 个提交，获得 {len(posts)} 个相关帖子")
                return posts  # 成功获取数据，退出重试循环

            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 403:
                    if attempt < max_retries - 1:
                        delay = base_delay * (2 ** attempt)
                        self.logger.warning(f"收集 r/{subreddit_name} 403错误，等待 {delay} 秒后重试...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"收集 r/{subreddit_name} 403错误 (多次重试失败)")
                else:
                    self.logger.error(f"收集 r/{subreddit_name} 失败: {e}")
                    break
            except Exception as e:
                self.logger.error(f"收集 r/{subreddit_name} 失败: {e}")
                break

        return posts
    
    def collect_data(self, start_date: datetime, end_date: datetime,
                    tickers: Optional[List[str]] = None,
                    subreddits: Optional[List[str]] = None,
                    limit_per_subreddit: int = 1000) -> Dict[str, int]:
        """
        收集Reddit数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            tickers: 目标股票代码列表
            subreddits: 目标子版块列表
            limit_per_subreddit: 每个子版块的帖子限制
        
        Returns:
            收集统计信息
        """
        if tickers:
            # 过滤股票关键词
            filtered_keywords = {k: v for k, v in self.ticker_keywords.items() 
                               if k in tickers}
            self.ticker_keywords = filtered_keywords
        
        target_subreddits = subreddits or self.target_subreddits
        
        self.logger.info(f"开始收集Reddit数据")
        self.logger.info(f"时间范围: {start_date} 到 {end_date}")
        self.logger.info(f"目标股票: {list(self.ticker_keywords.keys())}")
        self.logger.info(f"目标子版块: {target_subreddits}")
        
        all_posts = []
        stats = {'total_posts': 0, 'relevant_posts': 0, 'subreddits_processed': 0}
        
        for subreddit_name in target_subreddits:
            try:
                posts = self.collect_from_subreddit(
                    subreddit_name, start_date, end_date, limit_per_subreddit
                )
                all_posts.extend(posts)
                stats['subreddits_processed'] += 1
                stats['relevant_posts'] += len(posts)
                
                self.logger.info(f"r/{subreddit_name}: 收集到 {len(posts)} 个相关帖子")
                
                # 定期保存数据
                if len(all_posts) >= 100:
                    self.save_posts_by_date_and_ticker(all_posts)
                    all_posts = []
                    self.save_processed_posts()
                
            except Exception as e:
                self.logger.error(f"处理 r/{subreddit_name} 失败: {e}")
                continue
        
        # 保存剩余数据
        if all_posts:
            self.save_posts_by_date_and_ticker(all_posts)
        
        # 保存缓存
        self.save_processed_posts()
        
        stats['total_posts'] = len(self.processed_posts)
        
        return stats

def load_reddit_config() -> RedditConfig:
    """加载Reddit API配置"""
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'reddit-data-collector/1.0')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    if not client_id or not client_secret:
        raise ValueError(
            "Reddit API配置缺失！请在.env文件中设置:\n"
            "REDDIT_CLIENT_ID=your_client_id\n"
            "REDDIT_CLIENT_SECRET=your_client_secret\n"
            "REDDIT_USER_AGENT=your_app_name/1.0\n"
            "REDDIT_USERNAME=your_username (可选)\n"
            "REDDIT_PASSWORD=your_password (可选)\n\n"
            "获取Reddit API凭据: https://www.reddit.com/prefs/apps"
        )
    
    return RedditConfig(
        client_id=client_id,
        client_secret=client_secret,
        user_agent=user_agent,
        username=username,
        password=password
    )

def main():
    parser = argparse.ArgumentParser(description='Reddit实时数据收集器')
    parser.add_argument('--start-date', type=str, default='2024-12-01',
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2025-02-01',
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--tickers', nargs='+',
                       default=['AAPL', 'MSFT', 'NVDA', ],
                       help='目标股票代码')
    parser.add_argument('--subreddits', nargs='+',
                       help='目标子版块 (默认使用预设列表)')
    parser.add_argument('--output-dir', default='social_media_data',
                       help='输出目录')
    parser.add_argument('--limit-per-subreddit', type=int, default=1000,
                       help='每个子版块的帖子限制')
    parser.add_argument('--incremental', action='store_true',
                       help='增量更新模式')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = load_reddit_config()
        
        # 解析日期
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        
        # 增量更新：从昨天开始
        if args.incremental:
            start_date = datetime.now() - timedelta(days=1)
            end_date = datetime.now()
        
        # 创建收集器
        collector = RedditLiveCollector(config, args.output_dir)
        
        # 开始收集
        stats = collector.collect_data(
            start_date=start_date,
            end_date=end_date,
            tickers=args.tickers,
            subreddits=args.subreddits,
            limit_per_subreddit=args.limit_per_subreddit
        )
        
        # 显示统计
        print("\n" + "="*60)
        print("Reddit数据收集完成")
        print("="*60)
        print(f"处理子版块数: {stats['subreddits_processed']}")
        print(f"相关帖子数: {stats['relevant_posts']}")
        print(f"总处理帖子数: {stats['total_posts']}")
        print(f"数据保存在: {args.output_dir}")
        
    except Exception as e:
        print(f"收集失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
